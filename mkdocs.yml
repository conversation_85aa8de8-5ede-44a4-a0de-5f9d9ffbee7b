---
site_name: Flyimg
site_url: https://flyimg.io
copyright: Copyright &copy; 2024 Flyimg

repo_url: https://github.com/flyimg/flyimg
edit_uri: edit/main/docs/

extra_css:
  - assets/stylesheets/extra.css

theme:
  name: material
  logo: assets/images/flyimg.png
  favicon: assets/images/favicon.ico

  palette:
    # Palette toggle for light mode
    - media: "(prefers-color-scheme: light)"
      scheme: flyimg
      primary: dark-blue
      accent: teal
      toggle:
        icon: material/weather-sunny
        name: Switch to dark mode

    # Palette toggle for dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: light-blue
      accent: teal
      toggle:
        icon: material/weather-night
        name: Switch to light mode

  features:
    - content.action.view
    - content.action.edit
    - content.code.copy
    - navigation.footer
    - navigation.instant
    - navigation.top
    - toc.follow

  icon:
    repo: fontawesome/brands/github
    edit: material/pencil
    view: material/eye

extra:
  analytics:
    provider: google
    property: G-6QWHLG77D2

markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
  - admonition
  - attr_list
  - md_in_html
  - pymdownx.details
  - pymdownx.superfences
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.snippets:
      base_path:
        - "."

nav:
  - Welcome: index.md
  - Get Started:
      - Install: install.md
      - Helm Chart: helm-chart.md
      - Basic Usage: basic-usage.md
  - Docs:
      - Configuration: configuration.md
      - URL Options: url-options.md
      - Storage Options: storage-options.md
      - Security: security.md
      - Benchmark: benchmark.md
      - Unit Tests: unit-tests.md
      - Enable XDebug: enable-xdebug.md