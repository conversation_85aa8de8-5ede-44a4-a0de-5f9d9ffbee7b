# Application Options

## The file config/parameters.yml

Here are the app options you can configure with the [config/parameters.yml](https://github.com/flyimg/flyimg/blob/main/config/parameters.yml) these options operate at runtime, you don't need to rebuild the container or restart any service, all requests<sup><a name="footnote1">1</a></sup> will check this config.

### debug

_Defaults to:_ `false`
_Description:_ Enables debug mode, currently is used only for the tests, so there's no harm in leaving it as it is.

### log_level

_Defaults to:_ `error`
_Description:_ Log level, possible values are: debug, info, notice, warning, error

### enable_cronjob_cleanup

_Defaults to:_ `true`
_Description:_ # To enable the Cleanup Cronjob to purge the var/tmp folder

### cronjob_cleanup_interval

_Defaults to:_ `0 */5 * * *`
_Description:_ The cronjob interval to cleanup the var/tmp folder

### enable_avif

_Defaults to:_ `true`
_Description:_ Serve A<PERSON>F automatically to Browsers supporting it. You can always request an image in Avif format passing the `o_avif` [URL option key](https://github.com/flyimg/flyimg/blob/main/docs/url-options.md).

### enable_webp

_Defaults to:_ `true`
_Description:_ Serve WebP automatically to Browsers supporting it. You can always request an image in webP format passing the `o_webp` [URL option key](https://github.com/flyimg/flyimg/blob/main/docs/url-options.md).

### header_cache_days

_Defaults to:_ `365`
_Description:_ Number of days for header cache expires `max_age`, this is the header sent to the client or browser requesting the resource. You can pass cache busting parameters to the URL which will break cache in all modern proxies and Browsers.

### options_separator

_Defaults to:_ `,`
_Description:_ URL options are separated by default by comas `,` but you can change that to some other character, like `._~:[]@!$'()*+;` just be carefull that it doesn't conflict with the sintaz of options you are passing to the URL, there is no strict checking of separating characters.

!!! Important
    When changing `options_separator` in `config/parameters`, you need to change the `OPTIONS_SEPARATOR` value in `web/js/main.js`.

### restricted_domains

_Defaults to:_ `false`
_Description:_ This restricts fetching images for transformations only from _whitelisted domains_ (see `whitelist_domains`). A good measure of safety and to prevent abuse of your app from third parties is to set `restricted_domains` to `true`, this way the app will download and try to transform resources only from domains you trust or have control of.

### whitelist_domains

_Defaults to:_

```yml
    - domain-1.com
    - domain-2.com
```

_Description:_ If `restricted_domains` is enabled, put your whitelisted domains in this list, subdomains are also OK. For the [Digital Ocean Provisioning Script](https://github.com/flyimg/DigitalOcean-provision) you can set the restricted domains at the droplet provisioning step.

### disable_cache

_Defaults to:_ `false`
_Description:_ When set to true the generated image will be deleted from the cache in web/upload and served directly in the response


### storage_system

_Defaults to:_ `local`

More info at [Storage Options](storage-options.md).

