<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="favicon.ico">
    <title>{$title}</title>
    <link rel="stylesheet" href="/css/bootstrap.min.css" crossorigin="anonymous">
    <link rel="stylesheet" href="/css/main.css" crossorigin="anonymous">
    <link href="css/main.css" rel="stylesheet">
</head>
<body>
<div class="site-wrapper container-md">
    <div class="cover-container">
        <div class="inner cover">
            <div class="inner cover">
                <hr>
                <p>
                    <img alt="Flyimg"
                         src="/upload/w_300/https://raw.githubusercontent.com/flyimg/graphic-assets/master/logo/raster/flyimg-logo-rgb.png">
                </p>
                <h2 id="hello-from-flyimg">Hello from Flyimg</h2>

                <h3 id="image-title">The image above (logo) has been served using Flyimg.
                </h3>

                <p>how? Calling the source in the <code>&lt;img&gt;</code> like this:</p>

                <code>/upload/w_300/https://raw.githubusercontent.com/flyimg/graphic-assets/master/logo/raster/flyimg-logo-rgb.png</code>

                <p>It got transformed from the original file which was 6000px wide!</p>
                <p>Now, on the server, there is a cached version of this transformed image. There is no need to do the
                    transformation again next time someone requests this image.</p>
                <p>The best of all is that you get <em>Moz-jpeg</em> compression by default on all your JPEGs.</p>


                <h3 id="transformations">Transformations</h3>

                <p>You can do many different transformations to you images, like: <strong>quality</strong>,
                    <strong>face-crop</strong>, <strong>face-blur</strong>, <strong>width</strong>,
                    <strong>height</strong>,
                    <strong>crop</strong>, <strong>background</strong>, <strong>rotate</strong>, <strong>scale</strong>,
                    amongst many others.</p>
            </div>
            <h1>Try it now !</h1>
            <br>
            <hr>
            <div class="row g-3">
                <div class="col">
                    <figure class="figure">
                        <img id="source-image" alt="Original Image" src="https://mudawn.com/assets/butterfly-3000.jpg"
                             class="img-fluid">
                        <hr/>
                        <p class="lead">
                            Source image
                        </p>
                        <figcaption class="figure-caption alert alert-success" id="source-image-caption"></figcaption>
                    </figure>
                </div>
                <div class="col">
                    <div id="loading-spinner" class="spinner-border text-info" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div id="generated-image-div">
                        <figure class="figure">
                            <img src="https://raw.githubusercontent.com/flyimg/graphic-assets/master/logo/raster/flyimg-logo-rgb.png"
                                 alt="Dynamic Image" id="generated-image" class="img-fluid">
                            <hr/>
                            <p class="lead">
                                Generated image
                            </p>
                            <figcaption class="figure-caption alert alert-success"
                                        id="generated-image-caption"></figcaption>
                        </figure>

                    </div>
                </div>
            </div>
            <hr>
            <div class="row g-3">
                <div class="col-md" id="generated-url-container">
                    <div class="p-2 bg-info bg-opacity-10 border border-info rounded">
                        <div class="row">
                            <div class="col-sm">
                                <div id="generated-url" class="align"></div>
                            </div>
                            <div class="col-sm-1">
                                <button id="copy-btn" type="button" class="d-flex ms-auto btn-clipboard"
                                        aria-label="Copy to clipboard"
                                        data-bs-original-title="Copy to clipboard">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                         class="bi bi-clipboard" viewBox="0 0 16 16">
                                        <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"></path>
                                        <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="source-image-input" class="form-label float-start">Enter the source image URL</label>
                    <div class="float-start" id="url-info" data-bs-toggle="tooltip" data-bs-placement="right" data-bs-title="if your image's URL contains parameters, you need to URL encode the whole query string.">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                             class="bi bi-info-circle-fill" viewBox="0 0 16 16">
                            <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
                        </svg>
                    </div>

                    <input type="text" class="form-control" id="source-image-input" placeholder="Enter image URL"
                           value="https://mudawn.com/assets/butterfly-3000.jpg">
                    <div id="source-image-validation" class="invalid-feedback">
                        Please enter a source image URL.
                    </div>
                </div>
                <div class="form-group">
                    <button class="btn btn-outline-info" type="button" id="add-btn">
                        Add new Parameter
                    </button>
                    <div class="alert alert-danger" role="alert" id="error-options">
                        Please add at least one option.
                    </div>
                </div>
                <!-- Dynamic input fields will be added here -->
                <div id="dynamic-input-fields"></div>

                <button type="button" class="btn btn-primary mt-3" id="refresh-btn">Refresh generated image
                </button>
            </div>
            <hr/>
            <p><a href="https://github.com/flyimg/flyimg" target="_blank">Check the docs for more info</a>.</p>
            <p>Happy imaging!</p>
            <div id="version">Version: <span id="version-number">{$version}</span></div>
            <hr />
        </div>

    </div>

</div>
<script src="/js/bootstrap.bundle.min.js"></script>
<script type="text/javascript" src="/js/main.js"></script>
</body>
</html>