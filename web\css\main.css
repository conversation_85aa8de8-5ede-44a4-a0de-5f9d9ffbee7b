/*
 * Globals
 */

/* Links */
a,
a:focus,
a:hover {
    color: #fff;
}

/* Custom default button */
.btn-secondary,
.btn-secondary:hover,
.btn-secondary:focus {
    color: #333;
    text-shadow: none;
    /* Prevent inheritance from `body` */
    background-color: #fff;
    border: .05rem solid #fff;
}

/*
 * Base structure
 */

html,
body {
    height: 100%;
    background-color: #333;
}

body {
    color: #fff;
    text-align: left;
    text-shadow: 0 .05rem .1rem rgba(0, 0, 0, .5);
}

#loading-spinner,
#generated-image-div,
#error-options,
#source-image-caption,
#generated-url-container {
    display: none;
}

#generated-url {
    overflow-wrap: anywhere;
}

#error-options {
    margin: 10px 0;
}

#url-info{
    margin: 0 10px;
}

.btn-clipboard {
    display: block;
    padding: 0.5em;
    line-height: 1;
    color: var(--bs-body-color);
    background-color: var(--bd-pre-bg);
    border: 0;
    border-radius: 0.25rem;
}

#dynamic-input-fields .input-group div {
    margin: 5px 5px;
}

#version {
    border-bottom-color: rgba(226, 228, 233, 0.56);
    border-bottom-style: dotted;
    border-bottom-width: 1px;
    box-sizing: border-box;
    color: rgb(94, 139, 222);
    color-scheme: dark;
    cursor: pointer;
    display: inline;
    font-family: Roboto, -apple-system, "system-ui", Helvetica, Arial, sans-serif;
    font-feature-settings: "kern", "liga";
    font-size: 13.6px;
    height: auto;
    line-height: 21.76px;
    text-decoration-color: rgb(94, 139, 222);
    text-decoration-line: none;
    text-decoration-style: solid;
    text-decoration-thickness: auto;
    text-size-adjust: 100%;
    transition-behavior: normal;
    transition-delay: 0s;
    transition-duration: 0.125s;
    transition-property: color;
    transition-timing-function: ease;
    width: auto;
    word-break: break-word;
    -webkit-font-smoothing: antialiased;
    -webkit-print-color-adjust: exact;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
