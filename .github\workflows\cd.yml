---
name: CD
on:
  push:
    branches:
      - main

env:
  REGISTRY_IMAGE: flyimg/flyimg

jobs:  
  release:
    name: Create new Tag and Release
    runs-on: ubuntu-latest
    permissions:
      contents: write # to be able to publish a GitHub release
      issues: write # to be able to comment on released issues
      pull-requests: write # to be able to comment on released pull requests
      id-token: write # to enable use of OIDC for npm provenance
    outputs:
      new_tag_version: ${{ steps.tag_version.outputs.new_tag_version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GH_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "lts/*"

      - name: Install dependencies
        run: |
          npm install \
            @semantic-release/git \
            @semantic-release/changelog \
            @iwavesmedia/semantic-release-composer \
            -D

      - name: Dry run to get the next release version
        id: tag_version
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
        run: |
          NEXT_TAG_VERSION=$(npx semantic-release --dry-run | grep 'The next release version is' | sed -E 's/.* ([[:digit:].]+)$/\1/')
          echo "new_tag_version=$NEXT_TAG_VERSION" >> "$GITHUB_OUTPUT"

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
        run: npx semantic-release

  docker_publish:
    needs: release
    if: ${{ needs.release.outputs.new_tag_version != '' }}
    runs-on: ubuntu-latest
    name: Docker Build and Push
    outputs:
      new_tag_version: ${{ needs.release.outputs.new_tag_version }}
    strategy:
      fail-fast: false
      matrix:
        platform:
          - linux/amd64
          - linux/arm/v7
          - linux/arm/v8
          - linux/arm64
    steps:
      - name: Prepare
        run: |
          platform="${{ matrix.platform }}"
          echo "PLATFORM_PAIR=${platform//\//-}" >> "$GITHUB_ENV"

      - name: Code Checkout for Docker build
        uses: actions/checkout@v4
        with:
          ref: 'main'

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY_IMAGE }}
          tags: |
            ${{ needs.release.outputs.new_tag_version }}
            latest

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v5
        with:
          push: true
          context: .
          platforms: ${{ matrix.platform }}
          file: ./Dockerfile
          outputs: type=image,name=${{ env.REGISTRY_IMAGE }},push-by-digest=true,name-canonical=true,push=true
      -
        name: Export digest
        run: |
          mkdir -p /tmp/digests
          digest="${{ steps.docker_build.outputs.digest }}"
          touch "/tmp/digests/${digest#sha256:}"
      -
        name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ env.PLATFORM_PAIR }}
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1

  manifest:
    name: Create manifest list + push to Docker Hub
    runs-on: ubuntu-latest
    needs:
      - docker_publish
    outputs:
      new_tag_version: ${{ needs.docker_publish.outputs.new_tag_version }}
    steps:
      -
        name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: /tmp/digests
          pattern: digests-*
          merge-multiple: true
      -
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      -
        name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY_IMAGE }}
          tags: |
            ${{ needs.docker_publish.outputs.new_tag_version }}
            latest
      -
        name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      -
        name: Create manifest and push
        working-directory: /tmp/digests
        run: |
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf '${{ env.REGISTRY_IMAGE }}@sha256:%s ' *)
      -
        name: Inspect image
        run: |
          docker buildx imagetools inspect "${{ env.REGISTRY_IMAGE }}:${{ steps.meta.outputs.version }}"        

  deploy_demo:
    name: Deploy to Demo demo.flyimg.io
    runs-on: ubuntu-latest
    needs: manifest
    steps:
      - name: Deploy new version of Flyimg to the demo server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.FLYIMG_DEMO_HOST }}
          username: ${{ secrets.FLYIMG_DEMO_USERNAME }}
          port: ${{ secrets.FLYIMG_DEMO_PORT }}
          key: ${{ secrets.FLYIMG_DEMO_KEY }}
          command_timeout: 20m
          script: |
            cd /home/<USER>/
            bash -c "sed 's/VERSION/${{ needs.manifest.outputs.new_tag_version }}/g' flyimg-swarm.yaml.tpl > flyimg-swarm.yaml"
            bash -c "docker stack deploy -c flyimg-swarm.yaml flyimg --detach"
