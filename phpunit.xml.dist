<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" backupGlobals="false" colors="true" processIsolation="true" stopOnFailure="false" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.3/phpunit.xsd" cacheDirectory=".phpunit.cache" backupStaticProperties="false">
  <coverage/>
  <php>
    <env name="HOSTNAME_URL" value=""/>
    <env name="env" value="test"/>
    <env name="XDEBUG_MODE" value="coverage"/>
  </php>
  <testsuites>
    <testsuite name="Flyimg Test Suite">
      <directory suffix=".php">./tests/Core</directory>
    </testsuite>
  </testsuites>
  <source>
    <include>
      <directory suffix=".php">./src/</directory>
    </include>
  </source>
</phpunit>
