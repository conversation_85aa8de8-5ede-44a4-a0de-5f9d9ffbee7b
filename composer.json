{"name": "flyimg/flyimg", "description": "Server image resizing and cropping on the fly base on ImageMagick+MozJPEG", "version": "1.7.4", "type": "project", "keywords": ["image", "resize", "crop", "imageMagick", "flysystem", "mozjpeg"], "homepage": "https://github.com/flyimg/flyimg", "license": "AGPL-3.0", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://sadokferjani.me", "role": "Maintainer"}], "require": {"php": ">=8.1", "flyimg/silex": "0.0.1", "league/flysystem": "^3.0", "league/flysystem-aws-s3-v3": "^3.0", "monolog/monolog": "^3.7"}, "require-dev": {"phpunit/phpunit": "11.*", "symfony/process": "^7.0", "squizlabs/php_codesniffer": "3.*", "escapestudios/symfony2-coding-standard": "~3.0", "symfony/browser-kit": "^7.0"}, "autoload": {"psr-4": {"Core\\": "src\\Core"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}}